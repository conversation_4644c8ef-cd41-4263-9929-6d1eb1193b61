// profile-management.tsx
'use client';
import { useState, useCallback, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { cn } from '../../../../lib/utils';
import useClickOutside from '../../../../hooks/useClickOutside';
import { useUnifiedSession, authClient } from '../../../../lib/auth-client';
import { useUserInitials } from '../../../../hooks/useAvatarUpdate';
import { AvatarUpload } from '../../../ui/avatar-upload.client';
import { getImageUrl } from '../../../../lib/utils/image';
import { ThemeBackgroundDialog } from './theme-background-dialog.client';
import { useTheme } from '../../../providers/theme-provider.client';
import {UserIcon, RoleIcon, ColorThemeIcon} from "../../../ui/icons"
import {
  AtSign,
  Edit3,
  Settings,
  CreditCard,
  Calendar,
  Crown,
  Zap,
  Lock
} from 'lucide-react';
import {
  InputButton,
  InputButtonProvider,
  InputButtonAction,
  InputButtonSubmit,
  InputButtonInput,
} from '../../../ui/buttons/input';

interface ProfileManagementProps {
  className?: string;
}

export function ProfileManagement({ className }: ProfileManagementProps) {
  const { session } = useUnifiedSession();
  const user = session?.user;
  const userInitials = useUserInitials(user?.name);
  const { theme, backgroundTheme } = useTheme();

  // Process user image data
  const userImageUrl = getImageUrl(user?.image);
  
  // Name editing state (for non-students)
  const [editedName, setEditedName] = useState(user?.name || '');
  const [isSaving, setIsSaving] = useState(false);
  const [showNameInput, setShowNameInput] = useState(false);
  const [nameMessage, setNameMessage] = useState('');
  const [nameMessageType, setNameMessageType] = useState<'success' | 'error' | ''>('');

  // State for password editing (students only)
  const [showPasswordInput, setShowPasswordInput] = useState(false);
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordStep, setPasswordStep] = useState<'current' | 'new' | 'confirm'>('current');
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [passwordMessage, setPasswordMessage] = useState('');
  const [passwordMessageType, setPasswordMessageType] = useState<'success' | 'error' | ''>('');

  // Theme dialog state
  const [isThemeDialogOpen, setIsThemeDialogOpen] = useState(false);

  // Refs for click outside detection
  const nameFormRef = useRef<HTMLFormElement>(null);
  const passwordFormRef = useRef<HTMLFormElement>(null);

  // Sync editedName with user name when user changes
  useEffect(() => {
    setEditedName(user?.name || '');
  }, [user?.name]);

  // Handle click outside to close input
  useClickOutside(nameFormRef as any, () => {
    if (showNameInput && !isSaving) {
      setShowNameInput(false);
      setEditedName(user?.name || '');
      setNameMessage('');
      setNameMessageType('');
    }
  });

  // Handle avatar update
  const handleAvatarUpdate = useCallback((imageUrl: string | null) => {
    console.log('Avatar updated:', imageUrl);
    // The avatar update hook will handle the actual database update
    // and refresh the session, so the UI will update automatically
  }, []);

  const handleSaveEdit = useCallback(async () => {
    if (!user || editedName.trim() === user.name) {
      setShowNameInput(false);
      setNameMessage('');
      setNameMessageType('');
      return;
    }

    setIsSaving(true);
    setNameMessage('');
    setNameMessageType('');

    try {
      // TODO: Implement name update mutation
      console.log('Saving name update:', editedName.trim());

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Show success message
      setNameMessage('Name updated successfully!');
      setNameMessageType('success');

      // Auto-hide success message and close input after 3 seconds
      setTimeout(() => {
        setNameMessage('');
        setNameMessageType('');
        setShowNameInput(false);
      }, 3000);

    } catch (error) {
      console.error('Failed to update name:', error);
      setNameMessage('Failed to update name. Please try again.');
      setNameMessageType('error');
    } finally {
      setIsSaving(false);
    }
  }, [user, editedName]);

  // Handle name form submission
  const handleNameSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    if (editedName.trim() && editedName.trim() !== user?.name) {
      handleSaveEdit();
    }
  }, [editedName, user?.name, handleSaveEdit]);

  // Handle click outside to close password input
  useClickOutside(passwordFormRef as any, () => {
    if (showPasswordInput && !isChangingPassword) {
      setShowPasswordInput(false);
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
      setPasswordStep('current');
      setPasswordMessage('');
      setPasswordMessageType('');
    }
  });

  // Handle password change process
  const handlePasswordChange = useCallback(async () => {
    if (!user || user.role !== 'student') return;

    if (passwordStep === 'current') {
      if (!currentPassword.trim()) {
        setPasswordMessage('Please enter your current password');
        setPasswordMessageType('error');
        return;
      }
      setPasswordStep('new');
      setPasswordMessage('');
      setPasswordMessageType('');
      return;
    }

    if (passwordStep === 'new') {
      if (!newPassword.trim()) {
        setPasswordMessage('Please enter a new password');
        setPasswordMessageType('error');
        return;
      }
      if (newPassword.length < 6) {
        setPasswordMessage('Password must be at least 6 characters');
        setPasswordMessageType('error');
        return;
      }
      setPasswordStep('confirm');
      setPasswordMessage('');
      setPasswordMessageType('');
      return;
    }

    if (passwordStep === 'confirm') {
      if (confirmPassword !== newPassword) {
        setPasswordMessage('Passwords do not match');
        setPasswordMessageType('error');
        return;
      }

      setIsChangingPassword(true);
      setPasswordMessage('');
      setPasswordMessageType('');

      try {
        const result = await authClient.changePassword({
          currentPassword: currentPassword,
          newPassword: newPassword,
          revokeOtherSessions: false
        });

        if (result.error) {
          throw new Error(result.error.message || 'Failed to change password');
        }

        // Show success message
        setPasswordMessage('Password changed successfully!');
        setPasswordMessageType('success');

        // Auto-hide success message and close input after 3 seconds
        setTimeout(() => {
          setPasswordMessage('');
          setPasswordMessageType('');
          setShowPasswordInput(false);
          setCurrentPassword('');
          setNewPassword('');
          setConfirmPassword('');
          setPasswordStep('current');
        }, 3000);

      } catch (error: any) {
        console.error('Failed to change password:', error);
        setPasswordMessage(error.message || 'Failed to change password. Please try again.');
        setPasswordMessageType('error');
      } finally {
        setIsChangingPassword(false);
      }
    }
  }, [user, passwordStep, currentPassword, newPassword, confirmPassword]);

  // Handle password form submission
  const handlePasswordSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    handlePasswordChange();
  }, [handlePasswordChange]);

  // Get theme display name
  const getThemeDisplayName = useCallback((themeValue: string) => {
    switch (themeValue) {
      case 'light': return 'Light Mode';
      case 'dark': return 'Dark Mode';
      case 'system': return 'System (Auto)';
      default: return 'System (Auto)';
    }
  }, []);

  // Get background display name
  const getBackgroundDisplayName = useCallback((backgroundId: string) => {
    const backgroundNames: Record<string, string> = {
      'default': 'Default',
      'horizon-glow-bottom': 'Horizon Glow (Bottom)',
      'crimson-depth-bottom': 'Crimson Depth (Bottom)',
      'emerald-void-bottom': 'Emerald Void (Bottom)',
      'violet-abyss-bottom': 'Violet Abyss (Bottom)',
      'azure-depths-bottom': 'Azure Depths (Bottom)',
      'orchid-depths-bottom': 'Orchid Depths (Bottom)',
      'horizon-glow-top': 'Horizon Glow (Top)',
      'crimson-depth-top': 'Crimson Depth (Top)',
      'emerald-void-top': 'Emerald Void (Top)',
      'violet-abyss-top': 'Violet Abyss (Top)',
      'azure-depths-top': 'Azure Depths (Top)',
      'orchid-depths-top': 'Orchid Depths (Top)'
    };
    return backgroundNames[backgroundId] || 'Default';
  }, []);

  if (!user) {
    return (
      <div className="p-4 h-full flex items-center justify-center">
        <p className="text-white/70">Loading profile...</p>
      </div>
    );
  }

  // Get role display name
  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'student': return 'Student';
      case 'teacher': return 'Teacher';
      case 'school_admin': return 'School Administrator';
      default: return role;
    }
  };

  // Get role color
  const getRoleColor = (role: string) => {
    switch (role) {
      case 'student': return 'text-blue-400';
      case 'teacher': return 'text-green-400';
      case 'school_admin': return 'text-orange-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <>
      <div className={cn("mb-20", className)}>
        {/* Two Column Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Left Column - Profile Settings (2/3 width) */}
          <div className="lg:col-span-2 space-y-6">
            
            {/* Main Settings Container */}
            <div className="rounded-xl bg-white/85 dark:bg-zinc-900/85 backdrop-blur-md border border-white/5 dark:border-zinc-700/5 transition-all duration-200 overflow-hidden">
              
              {/* Account Section */}
              <div className="p-4 border-b border-black/5 dark:border-white/5">
                <div className="flex flex-col md:flex-row md:gap-8">
                  {/* Section Header */}
                  <div className="md:w-32 md:flex-shrink-0 mb-4 md:mb-0">
                    <div className="flex items-center gap-2">
                      <UserIcon className="h-5 w-5 text-black/70 dark:text-white/70" />
                      <h2 className="text-lg font-manrope_1 font-semibold text-black/90 dark:text-white/90">Account</h2>
                    </div>
                  </div>
                  
                  {/* Account Content */}
                  <div className="flex-1 space-y-4">
                    {/* User Info */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <AvatarUpload
                          currentImage={user.image}
                          userInitials={userInitials}
                          userName={user.name || user.email}
                          onImageUpdate={handleAvatarUpdate}
                          size="sm"
                          className=""
                        />
                        <div>
                          <p className="font-manrope_1 font-medium text-black/90 dark:text-white/90">
                            {user?.name || user?.email}
                          </p>
                          <p className="text-sm font-manrope_1 text-black/60 dark:text-white/60">
                            {user?.email}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={cn("text-sm font-manrope_1 font-medium", getRoleColor(user.role))}>
                          {getRoleDisplayName(user.role)}
                        </p>
                        <p className="text-xs font-manrope_1 text-black/50 dark:text-white/50">
                          Member since 2023
                        </p>
                      </div>
                    </div>

                    {/* Password Change (for students) */}
                    {user.role === 'student' && (
                      <div>
                        <form onSubmit={handlePasswordSubmit} className="w-full" ref={passwordFormRef}>
                          <InputButtonProvider
                            showInput={showPasswordInput}
                            setShowInput={setShowPasswordInput}
                            transition={{ type: 'spring', stiffness: 400, damping: 30 }}
                            className="relative group w-full items-center justify-center h-12 md:h-10 select-none rounded-full px-3 text-sm leading-8 transition-all duration-200 overflow-hidden bg-gradient-to-br from-zinc-100 to-zinc-200 text-zinc-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] hover:shadow-md dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:text-zinc-50 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] dark:hover:shadow-lg"
                          >
                            <InputButton>
                              <AnimatePresence>
                                {!showPasswordInput && (
                                  <motion.div
                                    key="password-display"
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    exit={{ opacity: 0 }}
                                    transition={{ duration: 0.2 }}
                                    className="w-full"
                                  >
                                    <InputButtonAction className="flex items-center justify-between w-full px-2">
                                      <span className="text-black/80 dark:text-white/80 font-manrope_1 text-sm">
                                        Click to change password
                                      </span>
                                      <Lock className="h-3 w-3 text-black/50 dark:text-white/50" />
                                    </InputButtonAction>
                                  </motion.div>
                                )}
                              </AnimatePresence>

                              <InputButtonSubmit
                                onClick={() => {}}
                                type="submit"
                                disabled={isChangingPassword || (
                                  passwordStep === 'current' && !currentPassword.trim()
                                ) || (
                                  passwordStep === 'new' && !newPassword.trim()
                                ) || (
                                  passwordStep === 'confirm' && (!confirmPassword.trim() || confirmPassword !== newPassword)
                                )}
                                message={passwordMessage}
                                messageType={passwordMessageType}
                                isSubmitting={isChangingPassword}
                              >
                                {isChangingPassword ? 'Changing...' :
                                 passwordStep === 'current' ? 'Continue' :
                                 passwordStep === 'new' ? 'Next' :
                                 'Change Password'}
                              </InputButtonSubmit>

                              {showPasswordInput && (
                                <InputButtonInput
                                  type="password"
                                  value={
                                    passwordStep === 'current' ? currentPassword :
                                    passwordStep === 'new' ? newPassword :
                                    confirmPassword
                                  }
                                  onChange={(e) => {
                                    if (passwordStep === 'current') {
                                      setCurrentPassword(e.target.value);
                                    } else if (passwordStep === 'new') {
                                      setNewPassword(e.target.value);
                                    } else {
                                      setConfirmPassword(e.target.value);
                                    }
                                  }}
                                  placeholder={
                                    passwordStep === 'current' ? 'Enter current password' :
                                    passwordStep === 'new' ? 'Enter new password' :
                                    'Confirm new password'
                                  }
                                  disabled={isChangingPassword}
                                  autoFocus
                                />
                              )}
                            </InputButton>
                          </InputButtonProvider>
                        </form>
                      </div>
                    )}

                    {/* Name Edit (for teachers and school admins) */}
                    {user.role !== 'student' && (
                      <div>
                        <form onSubmit={handleNameSubmit} className="w-full" ref={nameFormRef}>
                          <InputButtonProvider
                            showInput={showNameInput}
                            setShowInput={setShowNameInput}
                            transition={{ type: 'spring', stiffness: 400, damping: 30 }}
                            className="relative group w-full items-center justify-center h-12 md:h-10 select-none rounded-full px-3 text-sm leading-8 transition-all duration-200 overflow-hidden bg-gradient-to-br from-zinc-100 to-zinc-200 text-zinc-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] hover:shadow-md dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:text-zinc-50 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] dark:hover:shadow-lg"
                          >
                            <InputButton>
                              <AnimatePresence>
                                {!showNameInput && (
                                  <motion.div
                                    key="name-display"
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    exit={{ opacity: 0 }}
                                    transition={{ duration: 0.2 }}
                                    className="w-full"
                                  >
                                    <InputButtonAction className="flex items-center justify-between w-full px-2">
                                      <span className="text-black/80 dark:text-white/80 font-manrope_1 text-sm">
                                        {user?.name || 'Click to set display name'}
                                      </span>
                                      <Edit3 className="h-3 w-3 text-black/50 dark:text-white/50" />
                                    </InputButtonAction>
                                  </motion.div>
                                )}
                              </AnimatePresence>

                              <InputButtonSubmit
                                onClick={() => {}}
                                type="submit"
                                disabled={isSaving || !editedName.trim() || editedName.trim() === user?.name}
                                message={nameMessage}
                                messageType={nameMessageType}
                                isSubmitting={isSaving}
                              >
                                {isSaving ? 'Saving...' : 'Save Name'}
                              </InputButtonSubmit>

                              {showNameInput && (
                                <InputButtonInput
                                  type="text"
                                  value={editedName}
                                  onChange={(e) => setEditedName(e.target.value)}
                                  placeholder="Enter your display name"
                                  disabled={isSaving}
                                  autoFocus
                                />
                              )}
                            </InputButton>
                          </InputButtonProvider>
                        </form>
                      </div>
                    )}

                    {/* Username (for students) */}
                    {user.role === 'student' && (user as any).username && (
                      <div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <AtSign className="h-4 w-4 text-black/60 dark:text-white/60" />
                            <span className="text-sm font-manrope_1 text-black/80 dark:text-white/80">Username</span>
                          </div>
                          <span className="text-sm font-manrope_1 font-mono text-black/70 dark:text-white/70">
                            {(user as any).username}
                          </span>
                        </div>
                        <p className="text-xs font-manrope_1 text-black/50 dark:text-white/50 mt-1 ml-6">
                          Assigned by your teacher
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Options Section */}
              <div className="p-4">
                <div className="flex flex-col md:flex-row md:gap-8">
                  {/* Section Header */}
                  <div className="md:w-32 md:flex-shrink-0 mb-4 md:mb-0">
                    <div className="flex items-center gap-2">
                      <Settings className="h-5 w-5 text-black/70 dark:text-white/70" />
                      <h2 className="text-lg font-manrope_1 font-semibold text-black/90 dark:text-white/90">Options</h2>
                    </div>
                  </div>
                  
                  {/* Options Content */}
                  <div className="flex-1 space-y-4">
                    {/* Theme Settings */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <ColorThemeIcon className="h-4 w-4 text-black/60 dark:text-white/60" />
                        <div>
                          <p className="text-sm font-manrope_1 font-medium text-black/80 dark:text-white/80">
                            Theme & Background
                          </p>
                          <p className="text-xs font-manrope_1 text-black/50 dark:text-white/50">
                            {getThemeDisplayName(theme)} • {getBackgroundDisplayName(backgroundTheme)}
                          </p>
                        </div>
                      </div>
                      <button
                        onClick={() => setIsThemeDialogOpen(true)}
                        className="px-3 py-1.5 text-xs font-manrope_1 rounded-lg bg-black/5 hover:bg-black/10 dark:bg-white/5 dark:hover:bg-white/10 text-black/70 dark:text-white/70 transition-colors"
                      >
                        Customize
                      </button>
                    </div>

                    {/* Account Status */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                        <div>
                          <p className="text-sm font-manrope_1 font-medium text-black/80 dark:text-white/80">
                            Account Status
                          </p>
                          <p className="text-xs font-manrope_1 text-black/50 dark:text-white/50">
                            Your account is active and in good standing
                          </p>
                        </div>
                      </div>
                      <span className="text-xs font-manrope_1 text-green-400 font-medium">
                        Active
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Pricing (1/3 width) */}
          <div className="space-y-6">
            <div className="rounded-xl bg-white/85 dark:bg-zinc-900/85 backdrop-blur-md border border-white/5 dark:border-zinc-700/5 transition-all duration-200 overflow-hidden">
              
              {/* Current Plan Header */}
              <div className="p-6 border-b border-black/5 dark:border-white/5">
                <div className="text-center">
                  <div className="text-3xl font-manrope_1 font-bold text-black/90 dark:text-white/90 mb-1">
                    $0
                  </div>
                  <p className="text-sm font-manrope_1 text-black/60 dark:text-white/60">
                    Monthly plan
                  </p>
                  <div className="mt-4 flex items-center justify-center gap-2 text-xs font-manrope_1 text-black/50 dark:text-white/50">
                    <Calendar className="h-3 w-3" />
                    Next payment: Dec 31, 2039
                  </div>
                </div>
              </div>

              {/* Plan Details */}
              <div className="p-6 space-y-4">
                <div className="flex items-center gap-2 text-sm font-manrope_1 text-black/70 dark:text-white/70">
                  <Crown className="h-4 w-4" />
                  Member since 2023
                </div>
                
                <div className="space-y-3">
                  <button className="w-full px-4 py-2 text-sm font-manrope_1 rounded-lg bg-black/5 hover:bg-black/10 dark:bg-white/5 dark:hover:bg-white/10 text-black/70 dark:text-white/70 transition-colors flex items-center justify-center gap-2">
                    <CreditCard className="h-4 w-4" />
                    Billing history
                  </button>
                  
                  <button className="w-full px-4 py-2 text-sm font-manrope_1 rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white transition-all duration-200 flex items-center justify-center gap-2">
                    <Zap className="h-4 w-4" />
                    Update subscription
                  </button>
                </div>
              </div>

              {/* Payment Method */}
              <div className="p-6 border-t border-black/5 dark:border-white/5">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-sm font-manrope_1 font-medium text-black/80 dark:text-white/80">
                    Payment Method
                  </span>
                  <button className="text-xs font-manrope_1 text-blue-500 hover:text-blue-600 transition-colors">
                    Update
                  </button>
                </div>
                
                <div className="p-3 rounded-lg bg-gradient-to-br from-orange-400 to-red-500 text-white">
                  <div className="text-xs font-manrope_1 opacity-80 mb-1">•••• ••••</div>
                  <div className="text-sm font-manrope_1 font-medium">Unknown ••••</div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>

      {/* Theme Background Dialog */}
      <ThemeBackgroundDialog
        isOpen={isThemeDialogOpen}
        onOpenChange={setIsThemeDialogOpen}
      />
    </>
  );
}