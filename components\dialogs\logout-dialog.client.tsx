//components/dialogs/logout-dialog.client.tsx
'use client';

import React, { forwardRef } from 'react';
import { Dialog } from '@base-ui-components/react/dialog';
import { LogoutSection } from '../auth/shared/user-nav-content/logout-section.client';

interface LogoutDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export const LogoutDialog = forwardRef<HTMLDivElement, LogoutDialogProps>(
  ({ isOpen, onOpenChange }, ref) => {
    return (
      <Dialog.Root open={isOpen} onOpenChange={onOpenChange}>
        <Dialog.Portal>
          <Dialog.Backdrop className="fixed inset-0 backdrop-blur-lg opacity-100 transition-all duration-150 data-[ending-style]:opacity-0 data-[starting-style]:opacity-0 z-[100010]" />
          <Dialog.Popup
            ref={ref}
            className="fixed top-1/2 left-1/2 w-full max-w-[500px] -translate-x-1/2 -translate-y-1/2 transition-all duration-150 data-[ending-style]:scale-90 data-[ending-style]:opacity-0 data-[starting-style]:scale-90 data-[starting-style]:opacity-0 z-[100011] max-h-[90vh] overflow-hidden"
          >
            {/* Content */}
            <div className="p-2 md:p-4 bg-background/50">
              <LogoutSection onLogout={() => onOpenChange(false)} />
            </div>
          </Dialog.Popup>
        </Dialog.Portal>
      </Dialog.Root>
    );
  }
);

LogoutDialog.displayName = 'LogoutDialog';
