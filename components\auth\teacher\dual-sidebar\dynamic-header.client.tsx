'use client';

import { useState, useEffect, useRef } from 'react';
import { useLocation } from 'blade/hooks';
import { useDialogStore } from '../../../../lib/stores/dialog.store';
import { useUnifiedSession } from '../../../../lib/auth-client';
import { Popover } from '@base-ui-components/react/popover';
import { motion } from 'motion/react';
import { cn } from '../../../../lib/utils';
import { EmailIcon, SignoutIcon } from "../../../ui/icons"
import {
  Plus,
  ChevronDown,
  UserPlus,
  GraduationCap,
  School,
  Settings,
  Users,
  BookOpen,
  Calendar,
  MoreHorizontal,
  LogOut,
  User,
  X
} from 'lucide-react';

interface PageConfig {
  id: string;
  path: string;
  primaryAction: {
    label: string;
    icon: any;
    action: () => void;
    description: string;
  };
  secondaryActions: Array<{
    label: string;
    icon: any;
    action: () => void;
    description: string;
  }>;
}

export function DynamicHeader() {
  const location = useLocation();
  const { openStudentDialog, openGradeLevelDialog, openLogoutDialog } = useDialogStore();
  const { session } = useUnifiedSession();
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [isEmailPopoverOpen, setIsEmailPopoverOpen] = useState(false);


  
  // Spotlight animation state for Sign Out button
  const [signOutPosition, setSignOutPosition] = useState({ x: 0, y: 0 });
  const [signOutOpacity, setSignOutOpacity] = useState(0);
  const signOutRef = useRef<HTMLButtonElement>(null);

  // Track theme changes for popover styling
  useEffect(() => {
    const checkTheme = () => {
      setIsDarkMode(document.documentElement.classList.contains('dark'));
    };

    checkTheme();
    const observer = new MutationObserver(checkTheme);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });

    return () => observer.disconnect();
  }, []);

  // Spotlight handlers for Sign Out button
  const handleSignOutMouseMove = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (!signOutRef.current) return;
    const rect = signOutRef.current.getBoundingClientRect();
    setSignOutPosition({ x: e.clientX - rect.left, y: e.clientY - rect.top });
  };

  const handleSignOutMouseEnter = () => setSignOutOpacity(1);
  const handleSignOutMouseLeave = () => setSignOutOpacity(0);

  // Define page configurations
  const pageConfigs: PageConfig[] = [
    {
      id: 'students',
      path: '/students',
      primaryAction: {
        label: 'Add Student',
        icon: UserPlus,
        action: openStudentDialog,
        description: 'Invite new students to your classes'
      },
      secondaryActions: [
        {
          label: 'Create Grade Level',
          icon: GraduationCap,
          action: openGradeLevelDialog,
          description: 'Set up new grade levels for your students'
        },
        {
          label: 'Manage Classes',
          icon: School,
          action: () => {
            // Navigate to classes page - we'll implement this
            console.log('Navigate to classes');
          },
          description: 'View and manage your class settings'
        }
      ]
    },
    {
      id: 'classes',
      path: '/classes',
      primaryAction: {
        label: 'Create Grade Level',
        icon: GraduationCap,
        action: openGradeLevelDialog,
        description: 'Set up new grade levels and educational contexts'
      },
      secondaryActions: [
        {
          label: 'Add Students',
          icon: UserPlus,
          action: openStudentDialog,
          description: 'Invite students to your classes'
        },
        {
          label: 'Class Settings',
          icon: Settings,
          action: () => {
            console.log('Open class settings');
          },
          description: 'Configure class-specific settings'
        }
      ]
    },
    {
      id: 'calendar',
      path: '/calendar',
      primaryAction: {
        label: 'Add Event',
        icon: Calendar,
        action: () => {
          console.log('Add calendar event');
        },
        description: 'Schedule new events and activities'
      },
      secondaryActions: [
        {
          label: 'Add Students',
          icon: UserPlus,
          action: openStudentDialog,
          description: 'Invite students to your classes'
        },
        {
          label: 'Create Grade Level',
          icon: GraduationCap,
          action: openGradeLevelDialog,
          description: 'Set up new grade levels'
        }
      ]
    }
  ];

  // Get current page configuration
  const getCurrentPageConfig = (): PageConfig | null => {
    const pathname = location.pathname;
    
    // Check if we're on the profile page
    if (pathname.includes('/profile')) {
      return null; // We'll handle profile page separately
    }
    
    for (const config of pageConfigs) {
      if (pathname.includes(config.path)) {
        return config;
      }
    }
    
    // Default fallback for home/dashboard
    return {
      id: 'dashboard',
      path: '',
      primaryAction: {
        label: 'Quick Add',
        icon: Plus,
        action: () => setIsPopoverOpen(true),
        description: 'Quick access to common actions'
      },
      secondaryActions: [
        {
          label: 'Add Students',
          icon: UserPlus,
          action: openStudentDialog,
          description: 'Invite students to your classes'
        },
        {
          label: 'Create Grade Level',
          icon: GraduationCap,
          action: openGradeLevelDialog,
          description: 'Set up new grade levels'
        }
      ]
    };
  };

  const currentConfig = getCurrentPageConfig();
  const isProfilePage = location.pathname.includes('/profile');

  // Render profile page header
  if (isProfilePage) {
    return (
      <>
        <div className="flex items-center gap-3">
          {/* User Email Display */}
         {/* User Email Display - Desktop: full email, Mobile: popover button */}
          <Popover.Root open={isEmailPopoverOpen} onOpenChange={setIsEmailPopoverOpen}>
            <Popover.Trigger
              render={(props) => (
                <div
                  {...(props as any)}
                  className="flex items-center h-8 gap-2 px-3 py-1.5 bg-white/10 dark:bg-black/15 backdrop-blur-sm border border-black/15 dark:border-white/10 rounded-full cursor-pointer md:cursor-default transition-all duration-200 hover:bg-white/15 dark:hover:bg-black/20 md:hover:bg-white/10 md:dark:hover:bg-black/15"
                >
                  <EmailIcon className="w-4 h-4 text-black/70 dark:text-white/70" />
                  <span className="text-xs font-manrope_1 text-black dark:text-white hidden md:inline">
                    {session?.user?.email || '<EMAIL>'}
                  </span>
                </div>
              )}
            />
            <Popover.Portal>
              <Popover.Positioner sideOffset={8} align="start">
                <Popover.Popup className="md:hidden origin-[var(--transform-origin)] rounded-xl bg-zinc-100/90 dark:bg-zinc-900/90 backdrop-blur-lg border border-white/30 dark:border-zinc-700/30 px-4 py-3 text-black dark:text-white transition-[transform,scale,opacity] data-[ending-style]:scale-90 data-[ending-style]:opacity-0 data-[starting-style]:scale-90 data-[starting-style]:opacity-0 min-w-[200px] shadow-xl dark:shadow-2xl">
                  <div className="flex items-center gap-2">
                    <EmailIcon className="w-4 h-4 text-black/70 dark:text-white/70" />
                    <span className="text-sm font-manrope_1 text-black dark:text-white">
                      {session?.user?.email || '<EMAIL>'}
                    </span>
                  </div>
                </Popover.Popup>
              </Popover.Positioner>
            </Popover.Portal>
          </Popover.Root>


          {/* Sign Out Button with Spotlight Animation */}
          <button
            ref={signOutRef}
            onClick={() => openLogoutDialog()}
            onMouseMove={handleSignOutMouseMove}
            onMouseEnter={handleSignOutMouseEnter}
            onMouseLeave={handleSignOutMouseLeave}
            className="relative flex items-center gap-1.5 px-3 py-1.5 h-8 text-xs font-manrope_1 bg-red-100/5 dark:bg-red-900/5 backdrop-blur-sm border border-red-500/10 dark:border-red-500/10 text-red-700 dark:text-red-300 hover:bg-red-200/10 dark:hover:bg-red-800/10 rounded-full transition-all duration-200 shadow-sm dark:shadow-sm overflow-hidden"
            title="Sign out of your account"
          >
            {/* Spotlight effect */}
            <div
              className="pointer-events-none absolute inset-0 rounded-md border border-red-500/20 transition-opacity duration-500"
              style={{
                opacity: signOutOpacity,
                WebkitMaskImage: `radial-gradient(30% 30px at ${signOutPosition.x}px ${signOutPosition.y}px, black 45%, transparent)`,
                maskImage: `radial-gradient(30% 30px at ${signOutPosition.x}px ${signOutPosition.y}px, black 45%, transparent)`,
              }}
            />
            
            {/* Button content */}
            <div className="relative z-10 flex items-center md:gap-1.5">
              <SignoutIcon className="w-4 h-4" />
              <span className="hidden sm:inline">Sign Out</span>
            </div>
          </button>
        </div>
      </>
    );
  }

  if (!currentConfig) {
    return null;
  }

  const { primaryAction, secondaryActions } = currentConfig;

  return (
    <div className="flex items-center gap-2">
      {/* Primary Action Button - Glassmorphism style */}
      <button
        onClick={primaryAction.action}
        className="flex items-center gap-1.5 px-3 py-1.5 text-xs font-manrope_1 bg-white/15 dark:bg-black/20 backdrop-blur-sm border border-black/20 dark:border-white/10 text-black dark:text-white hover:bg-white/25 dark:hover:bg-black/30 rounded-full transition-all duration-200 shadow-lg dark:shadow-xl"
        title={primaryAction.description}
      >
        <primaryAction.icon className="w-3.5 h-3.5" />
        <span className="hidden sm:inline">{primaryAction.label}</span>
      </button>

      {/* Secondary Actions Popover */}
      {secondaryActions.length > 0 && (
        <Popover.Root open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
          <Popover.Trigger
            render={(props) => (
              <motion.button
                {...(props as any)}
                className="flex items-center gap-1 px-2 py-1.5 bg-zinc-100/10 dark:bg-zinc-900/15 backdrop-blur-sm border border-black/15 dark:border-white/10 text-black/80 dark:text-white/70 select-none hover:bg-white/20 dark:hover:bg-black/25 hover:text-black dark:hover:text-white/90 active:bg-white/25 dark:active:bg-black/30 rounded-full transition-all duration-200 shadow-lg dark:shadow-xl"
                animate={{
                  backgroundColor: isPopoverOpen
                    ? isDarkMode
                      ? 'rgba(0, 0, 0, 0.3)'
                      : 'rgba(255, 255, 255, 0.25)'
                    : undefined,
                  borderColor: isPopoverOpen
                    ? isDarkMode
                      ? 'rgba(255, 255, 255, 0.2)'
                      : 'rgba(255, 255, 255, 0.3)'
                    : undefined
                }}
                transition={{
                  duration: 0.2,
                  ease: "easeInOut"
                }}
                title="More actions"
              >
                <MoreHorizontal className="w-3.5 h-3.5" />
                <ChevronDown className={cn(
                  "w-3 h-3 opacity-60 transition-transform duration-200",
                  isPopoverOpen && "rotate-180"
                )} />
              </motion.button>
            )}
          />
          <Popover.Portal>
            <Popover.Positioner sideOffset={8} align="end">
              <Popover.Popup className="origin-[var(--transform-origin)] rounded-xl bg-zinc-100/90 dark:bg-zinc-900/90 backdrop-blur-lg border border-white/30 dark:border-zinc-700/30 px-2 py-2 text-black dark:text-white transition-[transform,scale,opacity] data-[ending-style]:scale-90 data-[ending-style]:opacity-0 data-[starting-style]:scale-90 data-[starting-style]:opacity-0 min-w-[220px] shadow-xl dark:shadow-2xl">
                
                <div className="space-y-1">
                  {secondaryActions.map((action, index) => (
                    <button
                      key={index}
                      onClick={() => {
                        action.action();
                        setIsPopoverOpen(false);
                      }}
                      className="w-full flex font-manrope_1 items-center gap-3 px-3 py-2 rounded-md text-left transition-all duration-200 hover:bg-white/60 dark:hover:bg-gray-800/60 hover:backdrop-blur-sm border border-transparent hover:border-zinc-100/20 dark:hover:border-zinc-600/20"
                    >
                      <action.icon className="w-4 h-4 flex-shrink-0 text-gray-600 dark:text-gray-400" />
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-manrope_1 font-medium text-gray-900 dark:text-white">
                          {action.label}
                        </div>
                        <div className="text-xs font-manrope_1 text-gray-600 dark:text-gray-400">
                          {action.description}
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              </Popover.Popup>
            </Popover.Positioner>
          </Popover.Portal>
        </Popover.Root>
      )}
    </div>
  );
}

