//components/dialog/DialogManager.client.tsx
'use client';

import { useRef } from 'react';
import { useAttention } from 'react-attention';
import { useHotkeys } from 'react-hotkeys-hook';
import { useDialogStore } from '../../lib/stores/dialog.store';
import { StudentManagementDialog } from '../auth/teacher/dialogs/student-management-dialog.client';
import { GradeLevelManagementDialog } from '../auth/teacher/dialogs/grade-level-management-dialog.client';
import { LogoutDialog } from './logout-dialog.client';

interface ClassItem {
  id: string;
  name: string;
  description?: string;
  teacherId: string;
  schoolId?: string;
  subjectId?: string;
  gradeLevel?: string;
  maxCapacity?: number;
  currentEnrollment?: number;
  isActive?: boolean;
  startDate?: string;
  endDate?: string;
  createdAt?: string;
  updatedAt?: string;
}

interface DialogManagerProps {
  teacherClasses?: ClassItem[];
}

// Hook for managing dialog state with react-attention
export function useDialogManager() {
  const {
    openStudentDialog,
    openGradeLevelDialog,
    openLogoutDialog,
    closeStudentDialog,
    closeGradeLevelDialog,
    closeLogoutDialog,
    closeAllDialogs,
  } = useDialogStore();

  return {
    openStudentDialog,
    openGradeLevelDialog,
    openLogoutDialog,
    closeStudentDialog,
    closeGradeLevelDialog,
    closeLogoutDialog,
    closeAllDialogs,
  };
}

export function DialogManager({ teacherClasses = [] }: DialogManagerProps) {
  const {
    isStudentDialogOpen,
    isGradeLevelDialogOpen,
    isLogoutDialogOpen,
    openLogoutDialog,
    closeStudentDialog,
    closeGradeLevelDialog,
    closeLogoutDialog
  } = useDialogStore();
  const studentDialogRef = useRef(null);
  const gradeLevelDialogRef = useRef(null);
  const logoutDialogRef = useRef(null);

  // Hotkey for logout dialog: Ctrl/Cmd + Shift + Q
  useHotkeys('ctrl+shift+q,cmd+shift+q', (event) => {
    event.preventDefault();
    event.stopPropagation();
    console.log('🎯 Logout dialog hotkey triggered (Ctrl+Shift+Q)');
    openLogoutDialog();
  }, {
    enableOnFormTags: true,
    preventDefault: true,
    enableOnContentEditable: false,
    enabled: true,
    ignoreModifiers: false
  }, [openLogoutDialog]);



  // Use react-attention for individual dialog management (not exclusive)
  useAttention(isStudentDialogOpen, closeStudentDialog, studentDialogRef);
  useAttention(isGradeLevelDialogOpen, closeGradeLevelDialog, gradeLevelDialogRef);
  useAttention(isLogoutDialogOpen, closeLogoutDialog, logoutDialogRef);

  return (
    <>
      {/* Student Management Dialog - Base layer (z-index: 50) */}
      <StudentManagementDialog
        isOpen={isStudentDialogOpen}
        onOpenChange={(open) => open ? null : closeStudentDialog()}
        teacherClasses={teacherClasses}
        ref={studentDialogRef}
      />

      {/* Grade Level Management Dialog - Middle layer (z-index: 60) */}
      <GradeLevelManagementDialog
        isOpen={isGradeLevelDialogOpen}
        onOpenChange={(open) => open ? null : closeGradeLevelDialog()}
        ref={gradeLevelDialogRef}
      />

      {/* Logout Dialog - Top layer (z-index: 70) */}
      <LogoutDialog
        isOpen={isLogoutDialogOpen}
        onOpenChange={(open) => open ? null : closeLogoutDialog()}
        ref={logoutDialogRef}
      />
    </>
  );
}