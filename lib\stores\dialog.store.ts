//lib/stores/dialo-store.ts
import { create } from 'zustand';

// Define the state and actions for the dialog store
interface DialogState {
  activeDialogs: ('student' | 'grade-level' | 'logout')[];
  isStudentDialogOpen: boolean;
  isGradeLevelDialogOpen: boolean;
  isLogoutDialogOpen: boolean;
  openStudentDialog: () => void;
  openGradeLevelDialog: () => void;
  openLogoutDialog: () => void;
  closeStudentDialog: () => void;
  closeGradeLevelDialog: () => void;
  closeLogoutDialog: () => void;
  closeAllDialogs: () => void;
}

// Create the Zustand store
export const useDialogStore = create<DialogState>((set) => ({
  activeDialogs: [],
  isStudentDialogOpen: false,
  isGradeLevelDialogOpen: false,
  isLogoutDialogOpen: false,

  openStudentDialog: () => {
    console.log('🎯 Dialog Store: Opening student dialog');
    set((state) => ({
      isStudentDialogOpen: true,
      activeDialogs: state.activeDialogs.includes('student')
        ? state.activeDialogs
        : [...state.activeDialogs, 'student']
    }));
  },

  openGradeLevelDialog: () => {
    console.log('🎯 Dialog Store: Opening grade level dialog');
    set((state) => ({
      isGradeLevelDialogOpen: true,
      activeDialogs: state.activeDialogs.includes('grade-level')
        ? state.activeDialogs
        : [...state.activeDialogs, 'grade-level']
    }));
  },

  openLogoutDialog: () => {
    set((state) => ({
      isLogoutDialogOpen: true,
      activeDialogs: state.activeDialogs.includes('logout')
        ? state.activeDialogs
        : [...state.activeDialogs, 'logout']
    }));
  },

  closeStudentDialog: () => {
    console.log('🎯 Dialog Store: Closing student dialog');
    set((state) => ({
      isStudentDialogOpen: false,
      activeDialogs: state.activeDialogs.filter(d => d !== 'student')
    }));
  },

  closeGradeLevelDialog: () => {
    console.log('🎯 Dialog Store: Closing grade level dialog');
    set((state) => ({
      isGradeLevelDialogOpen: false,
      activeDialogs: state.activeDialogs.filter(d => d !== 'grade-level')
    }));
  },

  closeLogoutDialog: () => {
    console.log('🎯 Dialog Store: Closing logout dialog');
    set((state) => ({
      isLogoutDialogOpen: false,
      activeDialogs: state.activeDialogs.filter(d => d !== 'logout')
    }));
  },

  closeAllDialogs: () => {
    console.log('🎯 Dialog Store: Closing all dialogs');
    set({
      activeDialogs: [],
      isStudentDialogOpen: false,
      isGradeLevelDialogOpen: false,
      isLogoutDialogOpen: false
    });
  },
}));
